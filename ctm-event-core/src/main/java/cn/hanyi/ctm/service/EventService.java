package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.group.QueryOptType;
import cn.hanyi.ctm.constant.group.QueryValueType;
import cn.hanyi.ctm.dto.PreviewNextIdDto;
import cn.hanyi.ctm.dto.event.CustomerEventQueryDto;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.event.UpdateAndActionDto;
import cn.hanyi.ctm.dto.event.ext.EventResourceDataDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryItemsDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.properites.EventResultQueryProperties;
import cn.hanyi.ctm.repository.CtmSurveyRepository;
import cn.hanyi.ctm.repository.EventGroupQueryRepository;
import cn.hanyi.ctm.repository.EventGroupRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import cn.hutool.core.util.ObjectUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.Permissions;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.service.OpenTenantContextService;
import org.befun.auth.service.SmsAccountService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.UserPermissions;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.repo.ResourcePermissionRepository;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.springframework.data.domain.Sort.Direction.DESC;


@Slf4j
@Service
public class EventService extends BaseService<Event, EventDto, EventRepository> {

    @Autowired
    private EventQueryService eventQueryService;
    @Autowired
    private CtmSurveyRepository surveyRepository;
    @Autowired
    private EventDownloadService eventDownloadService;
    @Autowired
    private OpenTenantContextService openTenantContextService;

    @Autowired
    private SmsAccountService smsAccountService;

    @Autowired
    private EventActionService actionService;

    @Autowired
    private UserService userService;

    @Autowired
    private EventGroupService eventGroupService;

    @Autowired
    private EventResultQueryProperties eventResultQueryProperties;

    @Autowired
    private EventGroupQueryRepository eventGroupQueryRepository;

    @Autowired
    private EventGroupRepository eventGroupRepository;

    @Autowired
    private ResourcePermissionRepository resourcePermissionRepository;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;
    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    public void afterMapToDto(List<Event> entity, List<EventDto> dto) {
        eventQueryService.insertQuestion(dto);
    }

    @Override
    public void afterMapToDto(Event entity, EventDto dto) {
        eventQueryService.insertRules(dto);
        dto.setResourceData(new EventResourceDataDto(
                smsAccountService.balance(TenantContext.getCurrentTenant())
        ));
        List<EventGroup> eventGroups = eventGroupService.findByEventId(entity.getId());
        if (!eventGroups.isEmpty()) {
            dto.setGroups(eventGroupService.mapToDto(eventGroups));
        }
        eventQueryService.insertCustomer(new ArrayList<>(Collections.singletonList(dto)));
        eventQueryService.insertChannelType(dto);
        fillResponseSequence(dto);
    }

    private void fillResponseSequence(EventDto dto) {
        List<Long> seq = jdbcTemplate.queryForList("select sequence from survey_response where id = " + dto.getResponseId(), Long.class);
        if (CollectionUtils.isNotEmpty(seq)) {
            dto.setResponseSequence(seq.get(0));
        }
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<Event>> EventDto updateOne(long id, S change) {
        Event et = require(id);
        EventDto ed = (EventDto) change;

        if (ed.getStatus() != null && !ed.getStatus().equals(et.getStatus())) {
            EventAction eventAction = actionService.addAction2(
                    userService.requireCurrentSimple(),
                    EventActionType.ACTION_TYPE_STATUS,
                    String.format("将状态更改为%s", ed.getStatus().desc),
                    EventActionStatusType.SUCCESS,
                    et
            );
            ctmEventTrigger.eventChangeStatus(
                    TenantContext.requireCurrentTenant(),
                    TenantContext.requireCurrentUserId(),
                    et.getId(),
                    eventAction.getId(),
                    ed.getStatus().name()
            );
        }
        return super.updateOne(id, change);
    }

    @Override
    public EventDto findOne(long id) {
        return checkPermissionReturn(id);
    }


    @Transactional
    public void updateAndAction(long id, UpdateAndActionDto dto) {
        String tags = dto.getTags();
        UpdateAndActionDto.Warning warning = dto.getWarning();
        String groups = dto.getGroups();

        if (StringUtils.isEmpty(tags) && warning == null && groups == null) {
            throw new BadRequestException("行动类型不能同时为空");
        }

        Event et = require(id);
        StringBuilder content = new StringBuilder();
        EventActionType actionType = null;

        if (StringUtils.isNotEmpty(tags) && !tags.equals(et.getTags())) {
            actionType = EventActionType.ACTION_TYPE_DEPARTMENT;

            String[] newTags = tags.split("/");
            String[] oldTags = StringUtils.isNotEmpty(et.getTags())
                    ? et.getTags().split("/")
                    : new String[]{};

            StringBuilder add = new StringBuilder();
            StringBuilder remove = new StringBuilder();

            for (String tag : newTags) {
                if (!Arrays.asList(oldTags).contains(tag)) {
                    add.append(tag).append("、");
                }
            }

            for (String tag : oldTags) {
                if (!Arrays.asList(newTags).contains(tag)) {
                    remove.append(tag).append("、");
                }
            }

            if (add.length() > 0) {
                add.deleteCharAt(add.length() - 1);
                content.append(String.format("增加所属部门%s", add));
            }
            if (remove.length() > 0) {
                if (add.length() > 0) {
                    content.append("，");
                }
                remove.deleteCharAt(remove.length() - 1);
                content.append(String.format("删除所属部门%s", remove));
            }
            et.setTags(tags);
        }


        if (warning != null && !warning.getWarningTitle().equals(et.getWarningTitle())) {
            if (warning.getIds().size() != warning.getWarningTitle().split(";").length) {
                throw new BadRequestException("预警规则和预警标题数量不一致");
            }
            actionType = EventActionType.ACTION_TYPE_WARNING;

            String[] newWarningTitle = warning.getWarningTitle().split(";");
            String[] oldWarningTitle = et.getWarningTitle().split(";");

            StringBuilder add = new StringBuilder();
            StringBuilder remove = new StringBuilder();

            for (String title : newWarningTitle) {
                if (!Arrays.asList(oldWarningTitle).contains(title)) {
                    add.append(title).append("、");
                }
            }

            for (String title : oldWarningTitle) {
                if (!Arrays.asList(newWarningTitle).contains(title)) {
                    remove.append(title).append("、");
                }
            }

            if (add.length() > 0) {
                add.deleteCharAt(add.length() - 1);
                content.append(String.format("增加触发规则%s", add));
            }
            if (remove.length() > 0) {
                content.append("，");
                remove.deleteCharAt(remove.length() - 1);
                content.append(String.format("删除规则%s", remove));
            }

            et.getWarnings().clear();
            warning.getIds().forEach(waringId -> {
                et.getWarnings().add(new EventWarningDto(et.getResponseId(), waringId));
            });
            et.setWarningTitle(warning.getWarningTitle());
        }

        if (groups != null) {

            List<String> groupContent = new ArrayList<>();
            Set<EventGroup> eventGroups = new HashSet<>(eventGroupService.findByEventId(id));


            // groups 为空则清空所有分组
            Set<Long> modifyList = StringUtils.isEmpty(groups)
                    ? new HashSet<>()
                    : Arrays.stream(groups.split(",")).map(Long::parseLong).collect(Collectors.toSet());

            eventGroups.addAll(eventGroupRepository.findAllById(modifyList));

            // 事件和组不是通过字段关联的，所以需要全部找到组做处理
            // 如果A移除了B的分组 那么B也是懵逼的 怎么突然就没有了
            // 产品说先不考虑权限 因为有行动日志
            eventGroups.forEach(g -> {
                List<EventGroupQuery> queries = g.getQueries();

                if (queries.isEmpty()) {
                    // 新增
                    EventGroupQuery query = new EventGroupQuery();
                    query.setGroup(g);

                    EventGroupQueryItemsDto itemsDto = new EventGroupQueryItemsDto();
                    itemsDto.setTemplateId(-1);
                    itemsDto.setPropertyName("id");
                    itemsDto.setQueryType(QueryOptType.in);
                    itemsDto.setQueryValueType(QueryValueType.selectMulti);
                    itemsDto.setQueryValue(String.valueOf(id));

                    query.setProperty(itemsDto);
                    eventGroupQueryRepository.save(query);

                    groupContent.add(String.format("增加所属分组%s", g.getTitle()));

                } else {
                    Iterator<EventGroupQuery> iterator = queries.stream().filter(q -> q.getProperty().getTemplateId() == -1).collect(Collectors.toSet()).iterator();
                    while (iterator.hasNext()) {
                        EventGroupQuery query = iterator.next();

                        if (modifyList.contains(g.getId())) {

                            if (query.getProperty().getQueryValue().equals(String.valueOf(id))) {
                                // 如果包含的话就不处理
                                continue;
                            } else {
                                // 新增
                                EventGroupQuery newQ = new EventGroupQuery();
                                newQ.setGroup(g);

                                EventGroupQueryItemsDto newItemsDto = new EventGroupQueryItemsDto();
                                newItemsDto.setTemplateId(-1);
                                newItemsDto.setPropertyName("id");
                                newItemsDto.setQueryType(QueryOptType.in);
                                newItemsDto.setQueryValueType(QueryValueType.selectMulti);
                                newItemsDto.setQueryValue(String.valueOf(id));

                                newQ.setProperty(newItemsDto);
                                eventGroupQueryRepository.save(newQ);

                                groupContent.add(String.format("增加所属分组%s", g.getTitle()));
                            }

                        } else {
                            // 如果不包含的话就删除
                            queries.remove(query);
                            groupContent.add(String.format("删除所属分组%s", g.getTitle()));
                        }


                    }
                }
                eventGroupRepository.save(g);
            });

            if (!groupContent.isEmpty()) {
                content.append(String.join("，", groupContent));
            }

            actionType = EventActionType.ACTION_TYPE_GROUP;
        }


        if (StringUtils.isNotEmpty(dto.getContent())) {
            content.append("：").append(dto.getContent());
        }

        if (actionType == null) {
            throw new BadRequestException("无更改内容");
        }

        actionService.addAction(
                userService.requireCurrentSimple(),
                actionType,
                content.toString(),
                EventActionStatusType.NONE,
                et
        );

        save(et);
    }

    public PreviewNextIdDto previewNext(long id, ResourceEntityQueryDto<EventDto> params) {
        Direction currentSort;
        params.setPage(1);

        if (params.getSorts().isUnsorted()) {
            currentSort = DESC;
            params.setSorts(Sort.by(Sort.Order.desc("createTime")));
        } else {
            currentSort = params.getSorts().get().findFirst().get().getDirection();
        }

        params.setSorts(params.getSorts().and(Sort.by(currentSort, "id")));

        QueryOperator previewLogic = QueryOperator.LESS_THAN;
        QueryOperator nextLogic = QueryOperator.GREATER_THAN;


        if (currentSort.isDescending()) {
            previewLogic = QueryOperator.GREATER_THAN;
            nextLogic = QueryOperator.LESS_THAN;
        }

        ResourceQueryCriteria previewParam = new ResourceQueryCriteria("id", id, previewLogic);
        ResourceQueryCriteria nextParam = new ResourceQueryCriteria("id", id, nextLogic);
        List<ResourceQueryCriteria> criteriaList = params.getQueryCriteriaList();

        criteriaList.add(nextParam);
        Page<EventDto> next = findAll(params);
        criteriaList.remove(nextParam);

        params.setSorts(
                currentSort.isDescending()
                        ? params.getSorts().ascending()
                        : params.getSorts().descending()
        );
        criteriaList.add(previewParam);
        Page<EventDto> preview = findAll(params);

        PreviewNextIdDto previewNextIdDto = new PreviewNextIdDto();
        params.setLimit(1);

        preview.getContent().stream().findFirst().ifPresent(i -> previewNextIdDto.setPreviewId((i.getId())));
        next.getContent().stream().findFirst().ifPresent(i -> previewNextIdDto.setNextId((i.getId())));
        return previewNextIdDto;
    }

    @Override
    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
        try {
            // 把 action 模拟成 event 的下一级
            EmbeddedFieldContext actions = new EmbeddedFieldContext(null, EventAction.class.getDeclaredField("event"), "event", ONE_TO_MANY);
            relationMaps.put("actions", actions);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 首先需要事件的查看菜单权限 {@link Permissions#EVENTS_EVENT_WARNING_VIEW}
     * 查看事件需要一些权限 1 or 2
     * 1 我有事件的数据权限
     * 2 我有事件的问卷的数据权限
     * 3 被分享了事件分组且没有开启部门权限
     */
    public EventDto checkPermissionReturn(long eventId) {
        // 这里是自调用无法触发增强使用注解的，所以需要手动调用一次
        openTenantContextService.addTenantContext();
        Boolean isAdmin = TenantContext.getCurrentIsAdmin();
        if (isAdmin != null && isAdmin) {
            return super.findOne(eventId);
        }

        UserPermissions userPermissions = TenantContext.getCurrentPermissions();
        List<String> actions;
        if (userPermissions == null || (actions = userPermissions.getAction()) == null) {
            throw new BadRequestException("无权限，请联系管理员");
        }
        if (actions.stream().noneMatch(i -> Permissions.EVENTS_EVENT_ACTION_VIEW.getPath().equals(i))) {
            throw new BadRequestException("无权限，请联系管理员");
        }

        long count = repository.count((Specification<Event>) (root, query, cb) -> cb.equal(root.get("id"), eventId));
        if (count <= 0) {
            // 事件分组协作
            // 1、找到是否存在分享 resourceType
            // 2、找到分组
            // 3、找到分组的后添加事件id查询
            if (resourcePermissionRepository.findAll((r, q, cb) -> {
                Long userId = TenantContext.getCurrentUserId();
                List<Long> roles = TenantContext.getCurrentRoleIds();
                return cb.and(cb.equal(r.get("resourceType"), ResourcePermissionType.EVENT_GROUP.name()), cb.or(r.get("userId").in(userId), r.get("roleId").in(roles)));
            }).stream().anyMatch(resourcePermission -> {
                ResourceEntityQueryDto<EventDto> resourceEntityQueryDto = new ResourceEntityQueryDto();
                ResourceEntityQueryDto<EventDto> query = resourceEntityQueryDto.addCriteria(new ResourceQueryCriteria("id", eventId));
                CountDto countDto = eventQueryService.countByGroupId(resourcePermission.getResourceId(), query);
                return countDto.getTotal() > 0;
            })) {
                return mapToDto(require(eventId));
            }

            throw new BadRequestException("无权限，请联系管理员");
        }
        return super.findOne(eventId);
    }

    public Page<EventDto> customerEvents(CustomerEventQueryDto dto) {
        ResourceEntityQueryDto<EventDto> query = dto.transform();
        query.addCriteria(new ResourceQueryCriteria("customerId", dto.getCustomerId()));
        query.addCriteria(new ResourceQueryCriteria("status", EventStatusType.NONE, QueryOperator.NOT_EQUAL));
        return super.findAll(query);
    }

    public ResourcePageResponseDto<EventDto> customQuery(ResourceEntityQueryDto<EventDto> params) {
        AtomicReference<Page<EventDto>> page = new AtomicReference<>(Page.empty());

        // 多处调用防止修改
        ResourceEntityQueryDto<EventDto> paramsCopy = ObjectUtil.cloneByStream(params);

        try {
            paramsCopy.getQueryCriteriaList().stream()
                    .filter(i -> "groups.id".equals(i.getParamKey()))
                    .findFirst().ifPresentOrElse(gp -> {
                        Long groupId = Long.valueOf(String.valueOf(gp.getValue()));
                        paramsCopy.getQueryCriteriaList().remove(gp);
                        page.set(eventQueryService.queryByGroupId(groupId, paramsCopy));
                    }, () -> {
                        page.set(findAll(paramsCopy));
                    });
        } catch (Exception e) {
            log.error("customQuery error", e);
        }


        return new ResourcePageResponseDto<>(page.get());
    }


    @SneakyThrows
    public CountDto customCount(ResourceEntityQueryDto<EventDto> params) {
        AtomicReference<CountDto> countDto = new AtomicReference<>(new CountDto(0));

        // 多处调用防止修改
        ResourceEntityQueryDto<EventDto> paramsCopy = ObjectUtil.cloneByStream(params);
        try {
            paramsCopy.getQueryCriteriaList().stream()
                    .filter(i -> "groups.id".equals(i.getParamKey()))
                    .findFirst().ifPresentOrElse(gp -> {
                        Long groupId = Long.valueOf(String.valueOf(gp.getValue()));
                        paramsCopy.getQueryCriteriaList().remove(gp);
                        countDto.set(eventQueryService.countByGroupId(groupId, paramsCopy));
                    }, () -> {
                        countDto.set(count(paramsCopy));
                    });
        } catch (Exception e) {
            log.error("customCount error", e);
        }


        return countDto.get();
    }
}
