package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.group.GroupType;
import cn.hanyi.ctm.constant.group.QueryLogic;
import cn.hanyi.ctm.dto.group.EventGroupQueryBuilderDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryPropertyDto;
import cn.hanyi.ctm.entity.EventGroup;
import cn.hanyi.ctm.entity.EventGroupDto;
import cn.hanyi.ctm.entity.EventGroupQuery;
import cn.hanyi.ctm.properites.EventResultQueryProperties;
import cn.hanyi.ctm.repository.EventGroupQueryRepository;
import cn.hanyi.ctm.repository.EventGroupRepository;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EventGroupService  extends BaseService<EventGroup, EventGroupDto, EventGroupRepository> {

    @Autowired
    private EventResultQueryProperties eventResultQueryProperties;

    @Autowired
    private EventGroupQueryRepository eventGroupQueryRepository;

    @Autowired
    private EventGroupRepository eventGroupRepository;

    public List<EventGroupQueryBuilderDto> getBuilder() {
        Function<String, List<EventGroupQueryBuilderDto.QueryBuilderItemDto>> getItems = t ->
                eventResultQueryProperties.getQueryType().getOrDefault(t, List.of())
                        .stream()
                        .map(j -> new EventGroupQueryBuilderDto.QueryBuilderItemDto(
                                j.getQueryTypeLabel(),
                                j.getQueryType(),
                                j.getQueryValueType()
                        ))
                        .collect(Collectors.toList());

        List<EventGroupQueryBuilderDto> builder = eventResultQueryProperties.getQuery()
                .stream().filter(i -> !i.getTemplateId().equals(-1)).map(i -> {
                    EventGroupQueryBuilderDto dto = new EventGroupQueryBuilderDto();
                    dto.setPropertyLabel(i.getPropertyLabel());
                    dto.setPropertyName(i.getPropertyName());
                    dto.setTemplateId(i.getTemplateId());
                    dto.setPropertySource(i.getPropertySource());
                    dto.setPropertyColumn(i.getPropertyColumn());
                    dto.setPropertyType(i.getPropertyType());
                    dto.setInputType(i.getInputType());
                    dto.setQueryItems(getItems.apply(i.getQueryItemType()));
                    return dto;
                })
                .collect(Collectors.toList());
        return builder;
    }

    public EventGroupQueryPropertyDto batchNewQuery(long groupId, EventGroupQueryPropertyDto dto) {
        EventGroup group = require(groupId);
        Optional.ofNullable(group.getLogic()).filter(i -> !QueryLogic.and.equals(i)).ifPresent(group::setLogic);
        group.getQueries().clear();

        dto.getItems().forEach(i -> {
            EventGroupQuery eventGroupQuery = new EventGroupQuery();
            eventGroupQuery.setProperty(i);
            eventGroupQuery.setGroup(group);
            eventGroupQuery.setSequence(group.getQueries().size());
            group.getQueries().add(eventGroupQuery);
        });

        save(group);
        return dto;
    }


    public List<EventGroup> findByEventId(Long eventId) {
        // 直接获取企业下所有的分组来查询对应的事件
        // 只查询手动分组

        return Optional.ofNullable(
                        scopeQuery(
                                EntityScopeStrategyType.ORGANIZATION,
                                () -> repository.findAll((r, q, cb) -> cb.equal(r.get("type"), GroupType.MANUAL))
                        )
                )
                .stream()
                .flatMap(List::stream)
                .filter(g -> g.getQueries().stream().anyMatch(q -> eventId.toString().equals(q.getProperty().getQueryValue()) && q.getProperty().getTemplateId().equals(-1)))
                .collect(Collectors.toList());
    }

}
